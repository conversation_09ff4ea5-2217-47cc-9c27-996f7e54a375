HOST=0.0.0.0
PORT=1387
APP_KEYS="toBeModified1,toBeModified2"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified

# Datenbank-Konfiguration
DATABASE_CLIENT=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=eulektro_terminal
DATABASE_USERNAME=eulektro_user
DATABASE_PASSWORD=your_secure_password
DATABASE_SSL=false

# Frontend-URL
FRONTEND_URL=https://terminal.eulektro.de
PUBLIC_URL=https://api.terminal.eulektro.de

# Custom Upload Provider Configuration
# Hauptverzeichnis für alle Uploads (relativ zum Projekt-Root oder absoluter Pfad)
UPLOAD_ROOT_DIR=./storage/uploads

# Basis-URL für den Zugriff auf hochgeladene Dateien
UPLOAD_BASE_URL=/storage

# Maximale Dateigröße in Bytes (Standard: 100MB)
UPLOAD_SIZE_LIMIT=*********

# Erlaubte MIME-Types (kommagetrennt, leer = alle erlaubt)
# UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf,video/mp4

# Unterverzeichnisse für verschiedene Dateitypen
UPLOAD_IMAGES_DIR=images
UPLOAD_DOCUMENTS_DIR=documents
UPLOAD_VIDEOS_DIR=videos
UPLOAD_OTHERS_DIR=files
UPLOAD_INVOICES_DIR=invoices
UPLOAD_LOGOS_DIR=logos

# Rechnungsordner (für PDF-Generierung)
INVOICE_FOLDER=/home/<USER>/terminal.eulektro.de/invoices
