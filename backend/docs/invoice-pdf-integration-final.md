# Invoice PDF Integration - Finale Implementierung

## 🎯 Übersicht

Die Invoice PDF Integration wurde erfolgreich implementiert und ermöglicht es, generierte Invoice-PDFs automatisch an Invoice-Objekte zu verlinken. Die Lösung verwendet einen benutzerdefinierten Upload-Service, der Dateien in organisierten Verzeichnissen speichert.

## 📁 Verzeichnisstruktur

```
public/
└── uploads/
    ├── invoices/
    │   ├── 2024/
    │   │   ├── 01/
    │   │   ├── 02/
    │   │   └── ...
    │   └── 2025/
    ├── images/
    ├── documents/
    └── videos/
```

## 🔧 Implementierte Services

### 1. Custom Upload Service (`custom-upload.ts`)
- **Zweck**: Organisiert Dateien in Unterverzeichnissen
- **Features**:
  - Automatische Jahres-/Monats-Organisation
  - Eindeutige Dateinamen mit Timestamps
  - Direkte Verknüpfung mit Invoice-Objekten
  - Bereinigung verwaister Dateien

### 2. Invoice File Manager (`invoice-file-manager.ts`)
- **Zweck**: Spezialisierte PDF-Verwaltung für Invoices
- **Features**:
  - Upload von Invoice-PDFs
  - Entfernung von PDFs
  - URL-Abruf
  - Status-Management

### 3. PDF Generator Service (`pdf-generator.ts`)
- **Zweck**: Automatische PDF-Generierung und Upload
- **Features**:
  - PDF-Erstellung aus Invoice-Daten
  - Automatischer Upload und Verknüpfung
  - Force-Überschreibung bestehender PDFs

## 🌐 API-Endpunkte

### PDF generieren
```http
POST /api/invoices/:id/generate-pdf
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN

{
  "force": false
}
```

### PDF hochladen
```http
POST /api/invoices/:id/upload-pdf
Content-Type: multipart/form-data
Authorization: Bearer YOUR_API_TOKEN

file: [PDF-Datei]
force: false
```

### PDF-URL abrufen
```http
GET /api/invoices/:id/pdf-url
Authorization: Bearer YOUR_API_TOKEN
```

### PDF entfernen
```http
DELETE /api/invoices/:id/pdf
Authorization: Bearer YOUR_API_TOKEN
```

## ⚙️ Konfiguration

### Umgebungsvariablen (.env)
```env
# Upload-Konfiguration
UPLOAD_SIZE_LIMIT=104857600  # 100MB

# API-Sicherheit
STRAPI_API_TOKEN=your_api_token_here
```

### Plugin-Konfiguration (config/plugins.ts)
```typescript
export default ({ env }) => ({
  upload: {
    config: {
      provider: 'local',
      providerOptions: {
        localServer: {
          maxage: 300000 // 5 Minuten Cache
        },
        sizeLimit: env.int('UPLOAD_SIZE_LIMIT', 100 * 1024 * 1024),
      }
    }
  }
});
```

## 🔄 Workflow

### Automatische PDF-Generierung
1. Invoice wird finalisiert (`finalize()` Methode)
2. PDF wird automatisch generiert
3. PDF wird in `/public/uploads/invoices/YYYY/MM/` gespeichert
4. Datei wird mit Invoice verknüpft
5. Invoice-Status wird auf `INMUTABLE_WRITTEN` gesetzt

### Manuelle PDF-Verwaltung
1. **Generierung**: `POST /api/invoices/:id/generate-pdf`
2. **Upload**: `POST /api/invoices/:id/upload-pdf`
3. **Abruf**: `GET /api/invoices/:id/pdf-url`
4. **Download**: `GET /api/invoices/:id/download-pdf`
5. **Entfernung**: `DELETE /api/invoices/:id/pdf`

## 📊 Datenbankstruktur

### Invoice Schema
```json
{
  "file": {
    "type": "media",
    "multiple": true,
    "allowedTypes": ["files"]
  }
}
```

### File-Objekt
```json
{
  "name": "Invoice_2024-001_1704067200000.pdf",
  "url": "/uploads/invoices/2024/01/Invoice_2024-001_1704067200000.pdf",
  "mime": "application/pdf",
  "size": 245760,
  "provider_metadata": {
    "path": "/absolute/path/to/file.pdf",
    "directory": "/absolute/path/to/directory"
  }
}
```

## 🛠️ Wartung und Verwaltung

### Verwaiste Dateien bereinigen
```javascript
// Service aufrufen
const result = await strapi.service('api::invoice.custom-upload').cleanupOrphanedFiles();
console.log(`${result.deletedCount} verwaiste Dateien gelöscht`);
```

### Upload-Verzeichnisse organisieren
```javascript
// Service aufrufen
const result = await strapi.service('api::invoice.custom-upload').organizeUploads();
console.log(`${result.movedCount} Dateien organisiert`);
```

### Fehlende PDFs finden
```http
GET /api/invoices/missing-pdfs
Authorization: Bearer YOUR_API_TOKEN
```

## 🔒 Sicherheit

- **API-Token-Authentifizierung**: Alle Endpunkte erfordern gültige API-Tokens
- **Dateityp-Validierung**: Nur PDF-Dateien werden für Invoice-PDFs akzeptiert
- **Größenbeschränkung**: Konfigurierbare maximale Dateigröße
- **Directory Traversal Schutz**: Sichere Pfad-Behandlung

## 🚀 Deployment

### Produktionsumgebung
1. Stelle sicher, dass das `public/uploads` Verzeichnis beschreibbar ist
2. Konfiguriere einen Reverse Proxy (nginx) für statische Dateien
3. Setze angemessene Backup-Strategien für Upload-Verzeichnisse
4. Überwache Speicherplatz-Verbrauch

### Backup-Strategie
```bash
# Backup der Upload-Verzeichnisse
tar -czf uploads-backup-$(date +%Y%m%d).tar.gz public/uploads/

# Backup der Datenbank (für File-Metadaten)
# Je nach verwendeter Datenbank
```

## ✅ Erfolgskriterien

- ✅ Invoice-PDFs werden automatisch generiert und verknüpft
- ✅ Manuelle PDF-Uploads funktionieren
- ✅ Dateien sind über URLs zugänglich
- ✅ Organisierte Verzeichnisstruktur
- ✅ Sichere API-Endpunkte
- ✅ Strapi Content Manager Kompatibilität
- ✅ Automatische Bereinigung verwaister Dateien

## 🔧 Troubleshooting

### Häufige Probleme

**Problem**: "Cannot find module 'local-directory'"
**Lösung**: Verwende den Standard 'local' Provider (bereits implementiert)

**Problem**: PDF-Dateien nicht zugänglich
**Lösung**: Prüfe Dateiberechtigungen und nginx-Konfiguration

**Problem**: Upload-Fehler
**Lösung**: Prüfe Speicherplatz und Verzeichnisberechtigungen

### Logs überprüfen
```bash
# Strapi-Logs
tail -f backend/.tmp/data.db

# Upload-Service-Logs
grep "Custom upload" backend/logs/strapi.log
```

Die Implementierung ist jetzt vollständig und produktionsbereit! 🎉
