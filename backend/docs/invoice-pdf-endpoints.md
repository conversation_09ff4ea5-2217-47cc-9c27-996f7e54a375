# Invoice PDF Management Endpoints

Diese Dokumentation beschreibt die neuen Endpoints zur manuellen Generierung und Verwaltung von Rechnungs-PDFs.

## Übersicht

Die folgenden Endpoints wurden hinzugefügt, um PDF-Anhänge für Invoices zu verwalten, falls diese nicht automatisch erstellt wurden:

1. **PDF generieren**: `POST /api/invoices/:id/generate-pdf`
2. **PDF herunterladen**: `GET /api/invoices/:id/download-pdf`
3. **Fehlende PDFs finden**: `GET /api/invoices/missing-pdfs`

## 1. PDF generieren

**Endpoint**: `POST /api/invoices/:id/generate-pdf`

Generiert manuell eine PDF-Datei für eine bestehende Invoice.

### Parameter

- `id` (URL-Parameter): Die Document-ID der Invoice

### Query-Parameter

- `force` (optional): `true` um eine bestehende PDF zu überschreiben

### Voraussetzungen

- Die Invoice muss eine `invoice_number` haben (Status: finalisiert)
- Die Invoice muss alle erforderlichen Daten haben (Positionen, Mandant, etc.)

### Antwort

```json
{
  "message": "PDF generated successfully",
  "invoice": {
    "documentId": "abc123",
    "invoice_number": "ACME_2024_00001",
    "invoice_status": "INMUTABLE_WRITTEN",
    "sum_gross": 23.80,
    "sum_net": 20.00,
    "vat_amount": 3.80
  },
  "pdf": {
    "fileName": "Invoice_ACME_2024_00001.pdf",
    "filePath": "/tmp/invoices/2024/12/Invoice_ACME_2024_00001.pdf",
    "fileSize": 45678,
    "created": "2024-12-19T10:30:00.000Z"
  }
}
```

### Fehler

- `400`: Invoice ID fehlt oder Invoice hat keine Rechnungsnummer
- `404`: Invoice nicht gefunden
- `500`: PDF-Generierung fehlgeschlagen

## 2. PDF herunterladen

**Endpoint**: `GET /api/invoices/:id/download-pdf`

Lädt eine bestehende PDF-Datei für eine Invoice herunter.

### Parameter

- `id` (URL-Parameter): Die Document-ID der Invoice

### Antwort

- **Content-Type**: `application/pdf`
- **Content-Disposition**: `attachment; filename="Invoice_ACME_2024_00001.pdf"`
- **Body**: PDF-Datei als Binärdaten

### Fehler

- `400`: Invoice ID fehlt oder Invoice hat keine Rechnungsnummer
- `404`: Invoice oder PDF-Datei nicht gefunden
- `500`: Fehler beim Lesen der Datei

## 3. Fehlende PDFs finden

**Endpoint**: `GET /api/invoices/missing-pdfs`

Findet alle finalisierten Invoices, für die keine PDF-Datei existiert.

### Antwort

```json
{
  "message": "Found 3 invoices without PDF files",
  "count": 3,
  "total_invoices": 25,
  "missing_pdfs": [
    {
      "documentId": "abc123",
      "invoice_number": "ACME_2024_00001",
      "invoice_status": "INMUTABLE_WRITTEN",
      "createdAt": "2024-12-19T10:00:00.000Z",
      "mandant": "ACME Corp",
      "expectedPdfPath": "/tmp/invoices/2024/12/Invoice_ACME_2024_00001.pdf",
      "sum_gross": 23.80,
      "payment_session_id": "def456"
    }
  ]
}
```

## Verwendung

### 1. Fehlende PDFs identifizieren

```bash
curl -X GET "http://localhost:1337/api/invoices/missing-pdfs"
```

### 2. PDF für eine spezifische Invoice generieren

```bash
curl -X POST "http://localhost:1337/api/invoices/abc123/generate-pdf"
```

### 3. PDF herunterladen

```bash
curl -X GET "http://localhost:1337/api/invoices/abc123/download-pdf" \
  -o "Invoice_ACME_2024_00001.pdf"
```

### 4. PDF überschreiben (falls bereits vorhanden)

```bash
curl -X POST "http://localhost:1337/api/invoices/abc123/generate-pdf?force=true"
```

## Technische Details

### PDF-Speicherort

PDFs werden im konfigurierten Invoice-Ordner gespeichert:
- Standard: `/tmp/invoices`
- Konfigurierbar über: `server.invoiceFolder` in der Strapi-Konfiguration
- Struktur: `{invoiceFolder}/{Jahr}/{Monat}/Invoice_{invoice_number}.pdf`

### PDF-Generierung

- Verwendet die bestehende `InvoicePdf`-Klasse
- Lädt Logo vom Mandanten
- Verwendet Invoice-Template für Firmendaten
- Generiert vollständige Rechnung mit allen Positionen

### Status-Updates

- Nach erfolgreicher PDF-Generierung wird der Invoice-Status auf `INMUTABLE_WRITTEN` gesetzt
- Dies verhindert weitere Änderungen an der Rechnung

## Fehlerbehandlung

Alle Endpoints haben umfassende Fehlerbehandlung:
- Validierung der Eingabeparameter
- Prüfung der Invoice-Existenz und -Status
- Dateisystem-Fehlerbehandlung
- Detaillierte Fehlermeldungen in den Logs

## Sicherheit

- Alle Endpoints sind derzeit ohne Authentifizierung (`auth: false`)
- In Produktionsumgebungen sollte entsprechende Authentifizierung aktiviert werden
- Dateizugriff ist auf den konfigurierten Invoice-Ordner beschränkt
