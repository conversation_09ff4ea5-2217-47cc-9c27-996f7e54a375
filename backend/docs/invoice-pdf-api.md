# Invoice PDF API Dokumentation

Diese API ermöglicht die Verwaltung von PDF-Dateien für Rechnungen mit dem benutzerdefinierten Upload Provider.

## Authentifizierung

Alle Endpunkte erfordern einen gültigen API-Token im Authorization-Header:

```
Authorization: Bearer YOUR_API_TOKEN
```

## Endpunkte

### 1. PDF generieren

**POST** `/api/invoices/:id/generate-pdf`

Generiert automatisch eine PDF für eine bestehende Rechnung.

**Parameter:**
- `id` (URL): Document-ID der Rechnung

**Request Body:**
```json
{
  "force": false  // Optional: Überschreibt existierende PDF
}
```

**Response:**
```json
{
  "message": "PDF generated and uploaded successfully",
  "file": {
    "id": "file_id",
    "url": "/storage/documents/Invoice_2024-001.pdf",
    "name": "Invoice_2024-001.pdf",
    "size": 245760
  },
  "invoice": {
    "documentId": "invoice_id",
    "invoice_status": "INMUTABLE_WRITTEN"
  }
}
```

### 2. PDF hochladen

**POST** `/api/invoices/:id/upload-pdf`

Lädt eine externe PDF-Datei für eine Rechnung hoch.

**Parameter:**
- `id` (URL): Document-ID der Rechnung

**Request Body (multipart/form-data):**
- `file`: PDF-Datei
- `force`: Boolean (optional) - Überschreibt existierende PDF

**Response:**
```json
{
  "message": "PDF uploaded and linked successfully",
  "file": {
    "id": "file_id",
    "url": "/storage/documents/Invoice_2024-001.pdf",
    "name": "Invoice_2024-001.pdf"
  },
  "invoice": {
    "documentId": "invoice_id",
    "invoice_status": "INMUTABLE_WRITTEN"
  }
}
```

### 3. PDF entfernen

**DELETE** `/api/invoices/:id/pdf`

Entfernt eine PDF-Datei von einer Rechnung.

**Parameter:**
- `id` (URL): Document-ID der Rechnung
- `fileId` (Query, optional): Spezifische Datei-ID

**Response:**
```json
{
  "message": "PDF file removed successfully",
  "removedFile": {
    "id": "file_id",
    "name": "Invoice_2024-001.pdf",
    "url": "/storage/documents/Invoice_2024-001.pdf"
  }
}
```

### 4. PDF-URL abrufen

**GET** `/api/invoices/:id/pdf-url`

Holt die URL der PDF-Datei für eine Rechnung.

**Parameter:**
- `id` (URL): Document-ID der Rechnung

**Response:**
```json
{
  "url": "/storage/documents/Invoice_2024-001.pdf",
  "file": {
    "id": "file_id",
    "name": "Invoice_2024-001.pdf",
    "size": 245760,
    "mime": "application/pdf"
  }
}
```

### 5. PDF herunterladen

**GET** `/api/invoices/:id/download-pdf`

Lädt die PDF-Datei direkt herunter.

**Parameter:**
- `id` (URL): Document-ID der Rechnung

**Response:**
- Content-Type: `application/pdf`
- Content-Disposition: `attachment; filename="Invoice_2024-001.pdf"`
- Body: PDF-Datei als Binary

### 6. Fehlende PDFs finden

**GET** `/api/invoices/missing-pdfs`

Findet alle Rechnungen ohne PDF-Dateien.

**Response:**
```json
{
  "message": "Found 5 invoices without PDF files",
  "count": 5,
  "total_invoices": 100,
  "missing_pdfs": [
    {
      "documentId": "invoice_id",
      "invoice_number": "2024-001",
      "invoice_status": "INMUTABLE_WRITTEN",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "mandant": "Mandant Name",
      "sum_gross": 119.00
    }
  ]
}
```

## Fehlerbehandlung

### Häufige Fehler

**400 Bad Request:**
```json
{
  "message": "PDF already exists for this invoice",
  "existingFile": {
    "id": "file_id",
    "name": "Invoice_2024-001.pdf"
  },
  "hint": "Use force=true to replace the existing PDF"
}
```

**404 Not Found:**
```json
{
  "message": "Invoice not found"
}
```

**500 Internal Server Error:**
```json
{
  "message": "Failed to generate PDF",
  "error": "Detailed error message"
}
```

## Verwendungsbeispiele

### cURL Beispiele

**PDF generieren:**
```bash
curl -X POST \
  https://your-api.com/api/invoices/invoice_123/generate-pdf \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"force": false}'
```

**PDF hochladen:**
```bash
curl -X POST \
  https://your-api.com/api/invoices/invoice_123/upload-pdf \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -F "file=@invoice.pdf" \
  -F "force=false"
```

**PDF-URL abrufen:**
```bash
curl -X GET \
  https://your-api.com/api/invoices/invoice_123/pdf-url \
  -H "Authorization: Bearer YOUR_API_TOKEN"
```

### JavaScript Beispiel

```javascript
// PDF generieren
const response = await fetch('/api/invoices/invoice_123/generate-pdf', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ force: false })
});

const result = await response.json();
console.log('PDF URL:', result.file.url);
```

## Konfiguration

Die PDF-Dateien werden automatisch im konfigurierten Upload-Verzeichnis gespeichert:

- **Standardpfad:** `./storage/uploads/documents/`
- **URL-Prefix:** `/storage/documents/`
- **Dateiname-Format:** `Invoice_{invoice_number}.pdf`

Die Konfiguration erfolgt über Umgebungsvariablen in der `.env` Datei.
