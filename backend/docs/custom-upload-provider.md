# Custom Upload Provider für Strapi

Dieser benutzerdefinierte Upload Provider ermöglicht es, Dateien an beliebigen Orten auf dem Server zu speichern, während die volle Kompatibilität mit dem Strapi Content Manager erhalten bleibt.

## Features

- **Flexible Speicherorte**: <PERSON><PERSON> können in jedem Verzeichnis auf dem Server gespeichert werden
- **Automatische Unterverzeichnisse**: Dateien werden basierend auf ihrem MIME-Type in Unterverzeichnisse sortiert
- **Strapi Content Manager Kompatibilität**: Vollständige Integration mit der Strapi Media Library
- **Sicherheit**: Validierung von Dateigröße und MIME-Types
- **Performance**: Effizientes Caching und statisches File Serving

## Konfiguration

### 1. Umgebungsvariablen

Fügen Sie folgende Variablen zu Ihrer `.env` Datei hinzu:

```env
# Hauptverzeichnis für Uploads
UPLOAD_ROOT_DIR=./storage/uploads

# URL-Prefix für den Zugriff auf Dateien
UPLOAD_BASE_URL=/storage

# Maximale Dateigröße (100MB)
UPLOAD_SIZE_LIMIT=104857600

# Unterverzeichnisse
UPLOAD_IMAGES_DIR=images
UPLOAD_DOCUMENTS_DIR=documents
UPLOAD_VIDEOS_DIR=videos
UPLOAD_INVOICES_DIR=invoices
UPLOAD_LOGOS_DIR=logos
```

### 2. Verzeichnisstruktur

Der Provider erstellt automatisch folgende Struktur:

```
storage/
└── uploads/
    ├── images/          # Bilder (JPEG, PNG, GIF, etc.)
    ├── documents/       # PDFs und Dokumente
    ├── videos/          # Video-Dateien
    ├── invoices/        # Rechnungs-PDFs
    ├── logos/           # Logo-Dateien
    └── files/           # Andere Dateitypen
```

## Verwendung

### Im Strapi Content Manager

Der Provider funktioniert transparent mit dem Standard Strapi Content Manager:

1. Öffnen Sie den Content Manager
2. Wählen Sie einen Content Type mit Media-Feldern
3. Laden Sie Dateien wie gewohnt hoch
4. Dateien werden automatisch in die entsprechenden Unterverzeichnisse sortiert

### Programmatische Verwendung

```typescript
// Datei hochladen
const uploadedFiles = await strapi.plugins.upload.services.upload.upload({
  data: {
    refId: entityId,
    ref: 'api::content-type.content-type',
    field: 'media'
  },
  files: {
    buffer: fileBuffer,
    name: 'example.pdf',
    type: 'application/pdf',
    size: fileBuffer.length
  }
});

// Datei löschen
await strapi.plugins.upload.services.upload.remove(file);
```

### Zugriff auf Dateien

Dateien sind über folgende URLs erreichbar:

```
https://your-domain.com/storage/images/filename.jpg
https://your-domain.com/storage/documents/document.pdf
https://your-domain.com/storage/invoices/invoice-123.pdf
```

## Erweiterte Konfiguration

### MIME-Type Beschränkungen

```env
# Nur bestimmte Dateitypen erlauben
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf
```

### Benutzerdefinierte Unterverzeichnisse

Passen Sie die Unterverzeichnisse in `config/plugins.ts` an:

```typescript
subDirectories: {
  images: 'bilder',
  documents: 'dokumente',
  contracts: 'vertraege',
  certificates: 'zertifikate'
}
```

## Sicherheit

- **Directory Traversal Schutz**: Verhindert Zugriff außerhalb des Upload-Verzeichnisses
- **MIME-Type Validierung**: Überprüfung der Dateitypen
- **Größenbeschränkung**: Konfigurierbare maximale Dateigröße
- **Sichere Dateinamen**: Automatische Generierung sicherer Dateinamen

## Troubleshooting

### Dateien nicht erreichbar

1. Prüfen Sie die Middleware-Konfiguration in `config/middlewares.ts`
2. Stellen Sie sicher, dass das Upload-Verzeichnis existiert und beschreibbar ist
3. Überprüfen Sie die URL-Konfiguration

### Upload-Fehler

1. Prüfen Sie die Dateigröße gegen `UPLOAD_SIZE_LIMIT`
2. Überprüfen Sie MIME-Type Beschränkungen
3. Stellen Sie sicher, dass das Zielverzeichnis beschreibbar ist

### Performance-Optimierung

1. Aktivieren Sie Gzip-Kompression für statische Dateien
2. Konfigurieren Sie einen Reverse Proxy (nginx) für bessere Performance
3. Verwenden Sie CDN für häufig abgerufene Dateien
