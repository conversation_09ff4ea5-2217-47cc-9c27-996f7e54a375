/**
 * Custom Static File Middleware
 * Serves files from custom upload directories
 */

import * as fs from 'fs';
import * as path from 'path';
import { Context, Next } from 'koa';

interface StaticOptions {
  root: string;
  prefix: string;
  maxAge?: number;
  index?: boolean;
}

export default (options: StaticOptions) => {
  const { root, prefix, maxAge = 300000, index = false } = options;
  const rootPath = path.resolve(root);

  return async (ctx: Context, next: Next) => {
    // Prüfe, ob die Anfrage mit dem konfigurierten Prefix beginnt
    if (!ctx.path.startsWith(prefix)) {
      return next();
    }

    // Entferne den Prefix aus dem Pfad
    const relativePath = ctx.path.substring(prefix.length);
    
    // Verhindere Directory Traversal Angriffe
    if (relativePath.includes('..') || relativePath.includes('\0')) {
      ctx.status = 400;
      ctx.body = 'Bad Request';
      return;
    }

    // Konstruiere den vollständigen Dateipfad
    const filePath = path.join(rootPath, relativePath);

    try {
      // Prüfe, ob die Datei existiert und eine reguläre Datei ist
      const stats = fs.statSync(filePath);
      
      if (!stats.isFile()) {
        if (stats.isDirectory() && !index) {
          ctx.status = 403;
          ctx.body = 'Directory listing disabled';
          return;
        }
        return next();
      }

      // Setze Content-Type basierend auf Dateierweiterung
      const ext = path.extname(filePath).toLowerCase();
      const mimeTypes: { [key: string]: string } = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.mp4': 'video/mp4',
        '.webm': 'video/webm',
        '.txt': 'text/plain',
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.zip': 'application/zip',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

      const contentType = mimeTypes[ext] || 'application/octet-stream';
      ctx.type = contentType;

      // Setze Cache-Header
      ctx.set('Cache-Control', `public, max-age=${Math.floor(maxAge / 1000)}`);
      ctx.set('Last-Modified', stats.mtime.toUTCString());

      // Prüfe If-Modified-Since Header für Caching
      const ifModifiedSince = ctx.get('If-Modified-Since');
      if (ifModifiedSince && new Date(ifModifiedSince) >= stats.mtime) {
        ctx.status = 304;
        return;
      }

      // Setze Content-Length
      ctx.length = stats.size;

      // Sende die Datei
      ctx.body = fs.createReadStream(filePath);

    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return next(); // Datei nicht gefunden, weiter zur nächsten Middleware
      }
      
      console.error('Error serving static file:', error);
      ctx.status = 500;
      ctx.body = 'Internal Server Error';
    }
  };
};
