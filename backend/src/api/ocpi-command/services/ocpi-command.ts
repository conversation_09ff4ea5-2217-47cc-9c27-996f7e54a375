/**
 * OCPI Session Service
 *
 * Implementiert die Logik für OCPI 2.2.1 Session-Befehle:
 * - startSession
 * - stopSession
 */

import { factories } from '@strapi/strapi';
import crypto from "crypto";
import { OCPIModule } from "../../ocpi-connection/services/ocpi-connection";
import { CommandResultType } from "../types/command-types";

// Importiere nur die benötigte Funktion aus dem Controller
import { cancelPayterSession } from "../controllers/ocpi-response";

// Temporär: Füge einen Debug-Log hinzu, um zu sehen, was importiert wird


// Interface für die OCPI-Antwort
interface OCPIResponse {
    status_code: number;
    status_message?: string;
    data?: any;
    timestamp: string;
}

// Interface für die OCPI-Fehlerantwort
interface OCPIErrorResponse {
    status_code: number;
    status_message: string;
    timestamp: string;
}

// Enum für OCPI-Statuscodes
enum OCPIStatusCode {
    SUCCESS = 1000,
    CLIENT_ERROR = 2000,
    SERVER_ERROR = 3000,
    HUB_ERROR = 4000,
    NOT_IMPLEMENTED = 2001,
    INVALID_PARAMETERS = 2002,
    NOT_FOUND = 2003,
    TIMEOUT = 2004
}

// Interface für die startSession-Parameter
interface StartSessionParams {
    evse_id: string;
    mandant_id?: string;
    payment_intent: string;
    contract_id?: string;
    serialNumber?: string;
}

// Interface für die startSession-Antwort
interface StartSessionResponse {
    success: boolean;
    response: OCPIResponse | OCPIErrorResponse;
}

// Interface für die stopSession-Parameter
interface StopSessionParams {
    session_id: string;
    evse_id?: string;
    mandant_id?: string;
}

// Interface für die stopSession-Antwort
interface StopSessionResponse {
    success: boolean;
    response: OCPIResponse | OCPIErrorResponse;
}

const ERROR_RESULTS = [
    CommandResultType.EVSE_OCCUPIED,
    CommandResultType.CANCELED_RESERVATION,
    CommandResultType.EVSE_INOPERATIVE,
    CommandResultType.FAILED,
    CommandResultType.NOT_SUPPORTED,
    CommandResultType.REJECTED,
    CommandResultType.UNKNOWN_RESERVATION
] as const;


export const generatePayterToken = (length: number) => {
    const prefix = "PAYTER";
    return (
        prefix +
        crypto.randomBytes(Math.ceil(length / 2))
            .toString("hex") // Konvertiere die zufälligen Bytes in einen hexadezimalen String
            .slice(0, length - prefix.length)
    ); // Schneide den String auf die gewünschte Länge zu
};

export default factories.createCoreService('api::ocpi-command.ocpi-command', ({ strapi }) => ({
    /**
     * Startet eine Ladesession
     *
     * OCPI 2.2.1 Command: START_SESSION
     *
     * @param {StartSessionParams} params - Parameter für den Start der Session
     * @returns {Promise<StartSessionResponse>} Antwort mit Erfolg/Misserfolg und OCPI-Antwort
     */
    async startSession(params: StartSessionParams): Promise<StartSessionResponse> {
        try {
            const {evse_id, mandant_id, payment_intent, contract_id = 'n/a', serialNumber = "n/a"} = params;

            // Validiere die erforderlichen Parameter
            if (!evse_id) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.INVALID_PARAMETERS,
                        'Missing required parameter: evse_id'
                    )
                };
            }

            if (!payment_intent) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.INVALID_PARAMETERS,
                        'Missing required parameter: payment_intent'
                    )
                };
            }

            const authorizationReference = payment_intent;

            const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
                documentId: evse_id,
                populate: {
                    location: {
                        populate: ['ocpiConnection']
                    }
                }
            });

            const connectorId = evse?.connectors?.[0]?.id || 1;

            const sessionStartTokenUid = generatePayterToken(20);
            const strapi_url = strapi.config.get('server.publicURL');
            // Erstelle den Payload für den START_SESSION-Befehl
            const payload = {
                response_url: `${strapi_url}/api/ocpi/commands/response/startSession/${authorizationReference}`,
                location_id: evse.location.ocpiId,
                evse_uid: evse?.uid,
                connector_id: connectorId,
                authorization_reference: authorizationReference,
                token: {
                    uid: sessionStartTokenUid,
                    type: "AD_HOC_USER ",
                    issuer: evse.location.ocpiConnection.companyName,
                    whitelist: "NEVER",
                    last_updated: new Date().toISOString(),
                    party_id: evse.location.ocpiConnection.partyId,
                    country_code: evse.location.ocpiConnection.countryCode,
                    contract_id: authorizationReference,
                    visual_number: serialNumber,
                    valid: true,
                },
            };

            // Logge den Payload
            console.log('START_SESSION payload:', JSON.stringify(payload));

            const synchronous_start_response = await strapi.service('api::ocpi-connection.ocpi-connection').postModule(evse.location.ocpiConnection, OCPIModule.COMMANDS, payload, '/START_SESSION');
            const data = await synchronous_start_response.json();

            if (synchronous_start_response.status !== 200 ||  ERROR_RESULTS.includes(data.data?.result)) {

                await cancelPayterSession(payment_intent, null);
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.SERVER_ERROR,
                        `Start session failed with status: ${synchronous_start_response.status}`
                    )
                };

            }
            // Erstelle die Antwort
            const response: OCPIResponse = {
                status_code: OCPIStatusCode.SUCCESS,
                status_message: 'Session start command sent successfully',
                data: {
                    authorization_reference: authorizationReference,
                    session_start_token_uid: sessionStartTokenUid,
                    evse_id: evse_id,
                    connector_id: connectorId,
                    location_id: evse.location.id
                },
                timestamp: new Date().toISOString()
            };

            return {
                success: true,
                response
            };

        } catch (error) {
            console.error('Error starting session:', error);

            return {
                success: false,
                response: createErrorResponse(
                    OCPIStatusCode.SERVER_ERROR,
                    `Error starting session: ${error.message || 'Unknown error'}`
                )
            };
        }
    },

    /**
     * Stoppt eine Ladesession
     *
     * OCPI 2.2.1 Command: STOP_SESSION
     *
     * @param {StopSessionParams} params - Parameter für das Stoppen der Session
     * @returns {Promise<StopSessionResponse>} Antwort mit Erfolg/Misserfolg und OCPI-Antwort
     */
    async stopSession(params: StopSessionParams): Promise<StopSessionResponse> {
        try {
            const { session_id, evse_id, mandant_id } = params;

            // Validiere die erforderlichen Parameter
            if (!session_id) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.INVALID_PARAMETERS,
                        'Missing required parameter: session_id'
                    )
                };
            }

            // Finde die OCPI-Session
            const ocpiSession = await strapi.documents('api::ocpi-session.ocpi-session').findFirst({
                filters: {
                    sessionId: session_id
                },
                populate: {
                    payment_session: {
                        populate: {
                            ocpi_evse: {
                                populate: {
                                    location: {
                                        populate: ['ocpiConnection']
                                    }
                                }
                            }
                        }
                    }
                }
            });

            if (!ocpiSession) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.NOT_FOUND,
                        'OCPI session not found'
                    )
                };
            }

            const evse = ocpiSession.payment_session?.ocpi_evse;
            if (!evse || !evse.location) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.NOT_FOUND,
                        'EVSE or location not found for session'
                    )
                };
            }

            const strapi_url = strapi.config.get('server.publicURL');

            // Erstelle den Payload für den STOP_SESSION-Befehl
            const payload = {
                response_url: `${strapi_url}/api/ocpi/commands/response/stopSession/${session_id}`,
                session_id: session_id
            };

            // Logge den Payload
            console.log('STOP_SESSION payload:', JSON.stringify(payload));

            const synchronous_stop_response = await strapi.service('api::ocpi-connection.ocpi-connection').postModule(
                evse.location.ocpiConnection,
                OCPIModule.COMMANDS,
                payload,
                '/STOP_SESSION'
            );

            const data = await synchronous_stop_response.json();

            if (synchronous_stop_response.status !== 200 || ERROR_RESULTS.includes(data.data?.result)) {
                return {
                    success: false,
                    response: createErrorResponse(
                        OCPIStatusCode.SERVER_ERROR,
                        `Stop session failed with status: ${synchronous_stop_response.status}`
                    )
                };
            }

            // Erstelle die Antwort
            const response: OCPIResponse = {
                status_code: OCPIStatusCode.SUCCESS,
                status_message: 'Session stop command sent successfully',
                data: {
                    session_id: session_id,
                    evse_id: evse.evseId,
                    location_id: evse.location.ocpiId
                },
                timestamp: new Date().toISOString()
            };

            return {
                success: true,
                response
            };

        } catch (error) {
            console.error('Error stopping session:', error);

            return {
                success: false,
                response: createErrorResponse(
                    OCPIStatusCode.SERVER_ERROR,
                    `Error stopping session: ${error.message || 'Unknown error'}`
                )
            };
        }
    }
}));

/**
 * Erstellt eine OCPI-Fehlerantwort
 *
 * @param {number} statusCode - OCPI-Statuscode
 * @param {string} statusMessage - Fehlermeldung
 * @returns {OCPIErrorResponse} OCPI-Fehlerantwort
 */
function createErrorResponse(statusCode: number, statusMessage: string): OCPIErrorResponse {
    return {
        status_code: statusCode,
        status_message: statusMessage,
        timestamp: new Date().toISOString()
    };
}
