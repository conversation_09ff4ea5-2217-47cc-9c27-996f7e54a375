
import { factories } from '@strapi/strapi';

// Nur Lese-Operationen für Invoices erlauben - nur mit API Token
const readOnlyRoutes = {
    routes: [
        {
            method: 'GET',
            path: '/invoices',
            handler: 'invoice.find',

        },
        {
            method: 'GET',
            path: '/invoices/:id',
            handler: 'invoice.findOne',

        }
    ]
};

// Benutzerdefinierte Routen
const customRoutes = {
    routes: [
        {
            method: 'POST',
            path: '/invoices/testendpunk',
            handler: 'invoice.testendpunkt',
            config: {
                auth: {
                    scope: ['api']
                },
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'POST',
            path: '/invoices/:id/generate-pdf',
            handler: 'invoice.generatePdf',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        },
        {
            method: 'POST',
            path: '/invoices/:id/upload-pdf',
            handler: 'invoice.uploadPdf',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        },
        {
            method: 'DELETE',
            path: '/invoices/:id/pdf',
            handler: 'invoice.removePdf',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        },
        {
            method: 'GET',
            path: '/invoices/:id/pdf-url',
            handler: 'invoice.getPdfUrl',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        },
        {
            method: 'GET',
            path: '/invoices/:id/download-pdf',
            handler: 'invoice.downloadPdf',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        },
        {
            method: 'GET',
            path: '/invoices/missing-pdfs',
            handler: 'invoice.findMissingPdfs',
            config: {
                auth: {
                    scope: ['api']
                }
            }
        }
    ]
};

// Kombiniere Lese-Routen und benutzerdefinierte Routen
export default {
    routes: [
        ...readOnlyRoutes.routes,
        ...customRoutes.routes
    ]
};