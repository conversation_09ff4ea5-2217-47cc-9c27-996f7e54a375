/**
 * Invoice File Manager Service
 * Verwaltet PDF-Dateien für Rechnungen mit dem benutzerdefinierten Upload Provider
 */

import * as fs from 'fs';
import * as path from 'path';

export default () => ({
  /**
   * Lädt eine Invoice-PDF hoch und verknüpft sie mit der Rechnung
   */
  async uploadInvoicePdf(invoiceDocumentId: string, pdfFilePath: string, force: boolean = false) {
    try {
      // Hole die Invoice mit allen benötigten Relationen
      const invoice = await strapi.documents('api::invoice.invoice').findOne({
        documentId: invoiceDocumentId,
        populate: {
          file: true,
          mandant: true
        }
      });

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceDocumentId}`);
      }

      if (!invoice.invoice_number) {
        throw new Error('Invoice must have an invoice number before PDF upload');
      }

      // Prüfe, ob bereits eine PDF-Datei existiert
      const existingPdfFile = invoice.file?.find(file => 
        file.name?.includes('Invoice_') && file.ext === '.pdf'
      );

      if (existingPdfFile && !force) {
        return {
          success: false,
          message: 'PDF already exists for this invoice',
          existingFile: existingPdfFile,
          hint: 'Use force=true to replace the existing PDF'
        };
      }

      // Prüfe, ob die PDF-Datei existiert
      if (!fs.existsSync(pdfFilePath)) {
        throw new Error(`PDF file not found at path: ${pdfFilePath}`);
      }

      const fileName = `Invoice_${invoice.invoice_number}.pdf`;
      const fileBuffer = fs.readFileSync(pdfFilePath);
      const stats = fs.statSync(pdfFilePath);

      // Lösche existierende PDF-Datei falls vorhanden und force=true
      if (existingPdfFile && force) {
        try {
          await strapi.plugins.upload.services.upload.remove(existingPdfFile);
          console.log(`Existing PDF file removed: ${existingPdfFile.url}`);
        } catch (removeError) {
          console.warn(`Could not remove existing PDF file: ${removeError.message}`);
        }
      }

      // Verwende den Custom Upload Service
      const uploadResult = await strapi.service('api::invoice.custom-upload').uploadInvoicePdf(
        invoice.documentId,
        pdfFilePath,
        fileName
      );

      if (!uploadResult.success) {
        throw new Error('Failed to upload PDF file');
      }

      const uploadedFile = uploadResult.file;

      // Aktualisiere die Invoice mit der Datei und setze den Status
      const updatedInvoice = await strapi.documents('api::invoice.invoice').update({
        documentId: invoiceDocumentId,
        data: {
          invoice_status: 'INMUTABLE_WRITTEN' // Rechnung ist jetzt unveränderlich
        },
        populate: {
          file: true,
          mandant: true
        }
      });

      console.log(`PDF successfully uploaded and linked to invoice ${invoice.invoice_number}: ${uploadedFile.url}`);

      return {
        success: true,
        message: 'PDF uploaded and linked successfully',
        file: uploadedFile,
        invoice: updatedInvoice
      };

    } catch (error) {
      console.error('Invoice PDF upload error:', error);
      throw error;
    }
  },

  /**
   * Entfernt eine PDF-Datei von einer Rechnung
   */
  async removeInvoicePdf(invoiceDocumentId: string, fileId?: string) {
    try {
      const invoice = await strapi.documents('api::invoice.invoice').findOne({
        documentId: invoiceDocumentId,
        populate: {
          file: true
        }
      });

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceDocumentId}`);
      }

      let fileToRemove;
      
      if (fileId) {
        // Spezifische Datei entfernen
        fileToRemove = invoice.file?.find(file => file.id === fileId);
      } else {
        // Erste PDF-Datei entfernen
        fileToRemove = invoice.file?.find(file => 
          file.name?.includes('Invoice_') && file.ext === '.pdf'
        );
      }

      if (!fileToRemove) {
        return {
          success: false,
          message: 'No PDF file found to remove'
        };
      }

      // Entferne die Datei
      await strapi.plugins.upload.services.upload.remove(fileToRemove);

      // Aktualisiere den Invoice-Status zurück auf DRAFT falls gewünscht
      await strapi.documents('api::invoice.invoice').update({
        documentId: invoiceDocumentId,
        data: {
          invoice_status: 'DRAFT'
        }
      });

      console.log(`PDF file removed from invoice: ${fileToRemove.url}`);

      return {
        success: true,
        message: 'PDF file removed successfully',
        removedFile: fileToRemove
      };

    } catch (error) {
      console.error('Invoice PDF removal error:', error);
      throw error;
    }
  },

  /**
   * Holt die PDF-URL für eine Rechnung
   */
  async getInvoicePdfUrl(invoiceDocumentId: string) {
    try {
      const invoice = await strapi.documents('api::invoice.invoice').findOne({
        documentId: invoiceDocumentId,
        populate: {
          file: true
        }
      });

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceDocumentId}`);
      }

      const pdfFile = invoice.file?.find(file => 
        file.name?.includes('Invoice_') && file.ext === '.pdf'
      );

      if (!pdfFile) {
        return {
          success: false,
          message: 'No PDF file found for this invoice'
        };
      }

      return {
        success: true,
        url: pdfFile.url,
        file: pdfFile
      };

    } catch (error) {
      console.error('Get invoice PDF URL error:', error);
      throw error;
    }
  },

  /**
   * Prüft, ob eine Rechnung bereits eine PDF-Datei hat
   */
  async hasInvoicePdf(invoiceDocumentId: string) {
    try {
      const result = await this.getInvoicePdfUrl(invoiceDocumentId);
      return result.success;
    } catch (error) {
      return false;
    }
  },

  /**
   * Organisiert Invoice-PDFs in Jahres-/Monatsordner
   */
  async organizeInvoicePdfs() {
    try {
      // Hole alle Invoices mit PDF-Dateien
      const invoices = await strapi.documents('api::invoice.invoice').findMany({
        filters: {
          file: {
            $notNull: true
          }
        },
        populate: {
          file: true
        }
      });

      let organizedCount = 0;

      for (const invoice of invoices) {
        if (!invoice.file || invoice.file.length === 0) continue;

        const pdfFile = invoice.file.find(file => 
          file.name?.includes('Invoice_') && file.ext === '.pdf'
        );

        if (!pdfFile) continue;

        try {
          // Organisiere die Datei basierend auf dem Erstellungsdatum der Rechnung
          const invoiceDate = new Date(invoice.createdAt);
          const year = invoiceDate.getFullYear();
          const month = String(invoiceDate.getMonth() + 1).padStart(2, '0');

          // Hier könnte die Datei in Unterordner organisiert werden
          // Das ist abhängig von der spezifischen Implementierung des Upload Providers
          
          organizedCount++;
          console.log(`Organized PDF for invoice ${invoice.invoice_number}: ${pdfFile.url}`);
        } catch (orgError) {
          console.warn(`Could not organize PDF for invoice ${invoice.invoice_number}: ${orgError.message}`);
        }
      }

      return {
        success: true,
        message: `Organized ${organizedCount} invoice PDFs`,
        count: organizedCount
      };

    } catch (error) {
      console.error('Organize invoice PDFs error:', error);
      throw error;
    }
  }
});
