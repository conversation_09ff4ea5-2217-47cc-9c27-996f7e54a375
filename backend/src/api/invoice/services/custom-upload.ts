/**
 * Custom Upload Service für Invoice PDFs
 * Erweitert den Standard-Upload-Provider um benutzerdefinierte Funktionalität
 */

import * as fs from 'fs';
import * as path from 'path';

export default () => ({
  /**
   * Lädt eine Datei hoch und organisiert sie in einem benutzerdefinierten Verzeichnis
   */
  async uploadInvoicePdf(invoiceDocumentId: string, filePath: string, fileName: string) {
    try {
      // Prüfe, ob die Datei existiert
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Hole die Invoice
      const invoice = await strapi.documents('api::invoice.invoice').findOne({
        documentId: invoiceDocumentId,
        populate: {
          mandant: true
        }
      });

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceDocumentId}`);
      }

      // Erstelle das Zielverzeichnis
      const uploadDir = path.resolve('./public/uploads');
      const invoiceDir = path.join(uploadDir, 'invoices');
      
      // Erstelle Unterverzeichnisse basierend auf Datum
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      
      const targetDir = path.join(invoiceDir, String(year), month);
      
      // Stelle sicher, dass das Verzeichnis existiert
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // Generiere einen eindeutigen Dateinamen
      const timestamp = Date.now();
      const ext = path.extname(fileName);
      const baseName = path.basename(fileName, ext);
      const uniqueFileName = `${baseName}_${timestamp}${ext}`;
      
      const targetPath = path.join(targetDir, uniqueFileName);

      // Kopiere die Datei
      fs.copyFileSync(filePath, targetPath);

      // Erstelle die URL relativ zum public-Verzeichnis
      const relativePath = path.relative('./public', targetPath);
      const fileUrl = `/${relativePath.replace(/\\/g, '/')}`;

      // Erstelle ein File-Objekt für Strapi
      const fileStats = fs.statSync(targetPath);
      
      const fileData = {
        name: uniqueFileName,
        alternativeText: `Rechnung ${invoice.invoice_number}`,
        caption: `PDF-Rechnung für ${invoice.invoice_number}`,
        width: null,
        height: null,
        formats: null,
        hash: `invoice_${invoice.invoice_number}_${timestamp}`,
        ext: ext,
        mime: 'application/pdf',
        size: fileStats.size,
        url: fileUrl,
        previewUrl: null,
        provider: 'local',
        provider_metadata: {
          path: targetPath,
          directory: targetDir
        },
        folderPath: `/invoices/${year}/${month}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Speichere die Datei-Informationen in der Strapi-Datenbank
      const savedFile = await strapi.documents('plugin::upload.file').create({
        data: fileData
      });

      // Verknüpfe die Datei mit der Invoice
      await strapi.documents('api::invoice.invoice').update({
        documentId: invoiceDocumentId,
        data: {
          file: [savedFile.id]
        }
      });

      console.log(`Invoice PDF uploaded successfully: ${fileUrl}`);

      return {
        success: true,
        file: savedFile,
        url: fileUrl,
        path: targetPath
      };

    } catch (error) {
      console.error('Custom upload error:', error);
      throw error;
    }
  },

  /**
   * Entfernt eine Invoice-PDF-Datei
   */
  async removeInvoicePdf(fileId: string) {
    try {
      // Hole die Datei-Informationen
      const file = await strapi.documents('plugin::upload.file').findOne({
        documentId: fileId
      });

      if (!file) {
        throw new Error(`File not found: ${fileId}`);
      }

      // Lösche die physische Datei
      const filePath = file.provider_metadata?.path;
      if (filePath && fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`Physical file deleted: ${filePath}`);
      }

      // Lösche den Datei-Eintrag aus der Datenbank
      await strapi.documents('plugin::upload.file').delete({
        documentId: fileId
      });

      console.log(`File record deleted: ${fileId}`);

      return {
        success: true,
        message: 'File removed successfully'
      };

    } catch (error) {
      console.error('Remove file error:', error);
      throw error;
    }
  },

  /**
   * Organisiert bestehende Upload-Dateien in Unterverzeichnisse
   */
  async organizeUploads() {
    try {
      const uploadDir = path.resolve('./public/uploads');
      
      // Erstelle Unterverzeichnisse
      const subDirs = ['invoices', 'images', 'documents', 'videos'];
      
      subDirs.forEach(subDir => {
        const dirPath = path.join(uploadDir, subDir);
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
          console.log(`Created directory: ${dirPath}`);
        }
      });

      // Hole alle Dateien aus der Datenbank
      const files = await strapi.documents('plugin::upload.file').findMany({
        filters: {},
        limit: 1000
      });

      let movedCount = 0;

      for (const file of files) {
        try {
          // Bestimme das Zielverzeichnis basierend auf MIME-Type
          let targetSubDir = 'documents';
          
          if (file.mime?.startsWith('image/')) {
            targetSubDir = 'images';
          } else if (file.mime === 'application/pdf' && file.name?.includes('Invoice_')) {
            targetSubDir = 'invoices';
          } else if (file.mime?.startsWith('video/')) {
            targetSubDir = 'videos';
          }

          // Konstruiere den aktuellen und neuen Pfad
          const currentPath = path.join('./public', file.url);
          const fileName = path.basename(file.url);
          const newDir = path.join(uploadDir, targetSubDir);
          const newPath = path.join(newDir, fileName);
          const newUrl = `/uploads/${targetSubDir}/${fileName}`;

          // Prüfe, ob die Datei bereits im richtigen Verzeichnis ist
          if (file.url === newUrl) {
            continue;
          }

          // Verschiebe die Datei falls sie existiert
          if (fs.existsSync(currentPath) && currentPath !== newPath) {
            fs.renameSync(currentPath, newPath);
            
            // Aktualisiere die URL in der Datenbank
            await strapi.documents('plugin::upload.file').update({
              documentId: file.documentId,
              data: {
                url: newUrl,
                provider_metadata: {
                  ...file.provider_metadata,
                  path: newPath,
                  directory: newDir
                }
              }
            });

            movedCount++;
            console.log(`Moved: ${currentPath} -> ${newPath}`);
          }

        } catch (fileError) {
          console.warn(`Could not organize file ${file.name}: ${fileError.message}`);
        }
      }

      return {
        success: true,
        message: `Organized ${movedCount} files`,
        movedCount
      };

    } catch (error) {
      console.error('Organize uploads error:', error);
      throw error;
    }
  },

  /**
   * Bereinigt verwaiste Dateien
   */
  async cleanupOrphanedFiles() {
    try {
      const uploadDir = path.resolve('./public/uploads');
      
      // Hole alle Dateien aus der Datenbank
      const dbFiles = await strapi.documents('plugin::upload.file').findMany({
        filters: {},
        limit: 10000
      });

      const dbFilePaths = new Set(dbFiles.map(file => {
        if (file.provider_metadata?.path) {
          return path.resolve(file.provider_metadata.path);
        }
        return path.resolve('./public', file.url);
      }));

      // Durchsuche das Upload-Verzeichnis nach physischen Dateien
      const physicalFiles = [];
      
      function scanDirectory(dir) {
        if (!fs.existsSync(dir)) return;
        
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
          const itemPath = path.join(dir, item);
          const stat = fs.statSync(itemPath);
          
          if (stat.isDirectory()) {
            scanDirectory(itemPath);
          } else {
            physicalFiles.push(path.resolve(itemPath));
          }
        });
      }

      scanDirectory(uploadDir);

      // Finde verwaiste Dateien
      const orphanedFiles = physicalFiles.filter(filePath => !dbFilePaths.has(filePath));
      
      let deletedCount = 0;
      
      // Lösche verwaiste Dateien (älter als 7 Tage)
      orphanedFiles.forEach(filePath => {
        try {
          const stats = fs.statSync(filePath);
          const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
          
          if (ageInDays > 7) {
            fs.unlinkSync(filePath);
            deletedCount++;
            console.log(`Deleted orphaned file: ${filePath}`);
          }
        } catch (error) {
          console.warn(`Could not delete orphaned file ${filePath}: ${error.message}`);
        }
      });

      return {
        success: true,
        message: `Cleaned up ${deletedCount} orphaned files`,
        deletedCount,
        totalOrphaned: orphanedFiles.length
      };

    } catch (error) {
      console.error('Cleanup error:', error);
      throw error;
    }
  }
});
