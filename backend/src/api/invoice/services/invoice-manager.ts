import {OmitRelationsWithoutTarget} from "@strapi/types/dist/modules/entity-service/params/attributes";
import {NumberValue} from "@strapi/types/dist/modules/documents/params/attributes";
import {InvoicePdf} from "./invoicePdf";
import * as fs from 'fs';
import {createInvoiceItems} from "./payment-capture";

type BaseInvoicePos = {
    title: string;
    unit: string;
    amount: number;
    description?: string;
    tax_rate: number;
}

export type InvoicePos = (
    BaseInvoicePos & { unit_price: number; unit_price_gross?: never; }
    ) | (
    BaseInvoicePos & { unit_price?: never; unit_price_gross: number; }
    );



function roundNumber(num: number): number {
    return Number(num.toFixed(2));
}

function getCurrentDayOfYear() {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const millisecondsPerDay = 1000 * 60 * 60 * 24;
    const diffInMilliseconds = now.valueOf() - startOfYear.valueOf();
    return Math.floor(diffInMilliseconds / millisecondsPerDay) + 1;
}

export class InvoiceManager {


    async createPreviewInvoice(payment_session_id: string) {
        // Find payment session with all needed relations
        const payment_session = await strapi.documents('api::payment-session.payment-session').findOne({
            documentId: payment_session_id,
            populate: {
                mandant: true,
                ocpi_cdr: true,
                ocpi_session: true,
                ocpi_evse: true
            }
        });

        if (!payment_session) {
            throw new Error(`Payment session not found: ${payment_session_id}`);
        }

        // Create draft invoice
        let newInvoice = await strapi.documents('api::invoice.invoice').create({
            data: {
                payment_session: payment_session.documentId,
                invoice_status: "DRAFT",
                mandant: payment_session.mandant.documentId,
                ocpi_cdr: payment_session?.ocpi_cdr?.documentId,
                ocpi_session: payment_session?.ocpi_session?.documentId,
                sum_net: 0,
                vat_amount: 0,
                sum_gross: 0,
                period_start_utc: payment_session.ocpi_cdr?.startDateTime ,
                period_end_utc: payment_session.ocpi_cdr?.endDateTime,
                invoice_positions: [],
                kindOfInvoice: "INVOICE",
                vat_percentage: 19,
                publishedAt: new Date(),
            },
            populate: {
                mandant: true
            }
        });

        const tarifDatetime = new Date(payment_session.showPriceDateTime);

        // If there's a CDR, create invoice positions
        if (payment_session.ocpi_cdr) {
            const cdr = payment_session.ocpi_cdr;
            const priceInfo = await strapi.service('api::tariff.tariff').getTariff(
                payment_session.ocpi_evse.documentId,
                tarifDatetime
            );

            const invoiceItems = createInvoiceItems(priceInfo, {
                totalEnergy: cdr.totalEnergy,
                totalTime: cdr.totalTime
            });

            // Füge die erzeugten Positionen der Rechnung hinzu
            for (const item of invoiceItems) {
                newInvoice = await this.addInvoiceItem(newInvoice.documentId, item);
            }
        }

        return newInvoice;
    }
    async calcPositionsSum(documentId: string) {
        const invoice = await strapi.documents('api::invoice.invoice').findOne({
            documentId: documentId,
            populate: {
                invoice_positions: true
            }
        });

        if (!invoice) {
            return undefined;
        }

        let sumGross = 0;
        let sumNet = 0;
        let vatAmount = 0;

        invoice.invoice_positions.forEach((item) => {
            if (item.unit_price && item.amount && item.tax_rate) {
                const itemNet = item.unit_price * item.amount;
                const itemVat = itemNet * (item.tax_rate / 100);
                const itemGross = itemNet + itemVat;

                sumNet += itemNet;
                vatAmount += itemVat;
                sumGross += itemGross;
            }
        });

        return await strapi.documents('api::invoice.invoice').update({
            documentId: invoice.documentId,
            data: {
                sum_gross: roundNumber(sumGross),
                sum_net: roundNumber(sumNet),
                vat_amount: roundNumber(vatAmount)
            }
        });
    }

    async addInvoiceItem(documentId: string, item: InvoicePos) {
        const invoice = await strapi.documents('api::invoice.invoice').findOne({
            documentId: documentId,
            populate: {
                invoice_positions: true
            }
        });

        if (!invoice) {
            throw new Error(`Invoice not found: ${documentId}`);
        }

        const position = invoice.invoice_positions?.length || 1;

        // Calculate net and gross based on input type
        let net: number;
        let gross: number;

        if (item?.unit_price_gross) {
            // If gross price is provided, calculate backwards
            gross = item.unit_price_gross * item.amount;
            net = gross / (1 + item.tax_rate / 100);
        } else if (item?.unit_price) {
            // If net price is provided, calculate forward
            net = item.unit_price * item.amount;
            gross = net * (1 + item.tax_rate / 100);
        } else {
            throw new Error('Either unit_price or unit_price_gross must be provided');
        }

        const tax = gross - net;

        const newItem = {
            pos: position,
            title: item.title,
            amount: item.amount,
            unit: item.unit,
            unit_price: item.unit_price !== undefined ? item.unit_price : roundNumber(net / item.amount),
            description: item.description,
            sum_net: roundNumber(net),
            sum_tax: roundNumber(tax),
            sum_gross: roundNumber(gross),
            tax_rate: item.tax_rate || 19
        };

        await strapi.documents('api::invoice.invoice').update({
            documentId: invoice.documentId,
            data: {
                invoice_positions: [
                    ...invoice.invoice_positions,
                    newItem
                ] as unknown as OmitRelationsWithoutTarget<"invoice-position.invoice-position", {
                    description?: string;
                    unit_price?: NumberValue;
                } & {
                    sum_gross: NumberValue;
                    sum_net: NumberValue;
                    tax_rate: NumberValue;
                    sum_tax: NumberValue;
                    title: string;
                    unit: string;
                    amount: NumberValue;
                    pos: number;
                }>[]
            },
            populate: {
                invoice_positions: true
            }
        });


        return await this.calcPositionsSum(invoice.documentId);
    }

    async generateInvoiceNumber(documentId: string) {

        const invoice = await strapi.documents('api::invoice.invoice').findOne({
            documentId: documentId,
            populate: {
                mandant: true
            }
        });
        // Atomare Transaktion um Race Conditions zu vermeiden
        const result = await strapi.db.transaction(async () => {
            // Hole die letzte Rechnung für diesen Mandanten und Rechnungstyp
            const latest = await strapi.documents('api::invoice.invoice').findFirst({
                filters: {
                    invoiceId: {
                        $notNull: true,
                    },
                    mandant: {
                        documentId: invoice.mandant?.documentId
                    },
                    kindOfInvoice: "INVOICE",
                    invoice_status: {
                        $in: ["INMUTABLE_WRITTEN", "PAID"]
                    }
                },
                orderBy: {
                    invoiceId: "desc",
                },
            });

            // Nächste InvoiceId bestimmen
            const nextInvoiceId = latest?.invoiceId ? latest.invoiceId + 1 : 1;

            // Jahr und Tag für die Rechnungsnummer
            const year = new Date().getFullYear();
            const dayOfYear = getCurrentDayOfYear();
            const today = new Date().toISOString().split('T')[0]; // z.B. '2024-06-07'

            // Prefix aus dem Invoice Template des Mandanten holen
            const template = await strapi.documents('api::invoice-template.invoice-template').findFirst({
                filters: {
                    mandant: {
                        documentId: invoice.mandant?.documentId
                    },
                    from: {
                        $lte: today
                    },
                    to: {
                        $gte: today
                    }

                }

            });
            // Wenn kein Prefix im Template, nimm die ersten 3 Buchstaben des Mandantennamens
            const prefix = template?.invoice_prefix || invoice.mandant.name.substring(0, 3).toUpperCase();

            // Rechnungsnummer im Format: PREFIX_YEAR_DAY_ID
            // z.B. ACME_2024_031_00042
            const invoiceNumber = `${prefix}_${year}_${nextInvoiceId.toString().padStart(5, '0')}`;

            const updatedInvoice = await strapi.documents('api::invoice.invoice').update({
                documentId: documentId,
                data: {
                    invoice_number: invoiceNumber,
                    invoiceId: nextInvoiceId
                },
                populate: {
                    invoice_positions:true,
                    mandant: true,
                    ocpi_cdr: true, // damit die CDRid auf die rechnung kommt
                }

            });

            return updatedInvoice;
        });

        return result;
    }

    /**
     * Generiert eine PDF für eine bestehende Invoice
     * @param documentId Die Document-ID der Invoice
     * @returns Promise mit der aktualisierten Invoice und PDF-Informationen
     */
    async generatePdfForInvoice(documentId: string) {
        // Hole die Invoice mit allen benötigten Relationen
        const invoice = await strapi.documents('api::invoice.invoice').findOne({
            documentId: documentId,
            populate: {
                mandant: {
                    populate: ['logo']
                },
                invoice_positions: true,
                ocpi_cdr: true,
                ocpi_session: true,
                payment_session: true,
                file: true
            }
        });

        if (!invoice) {
            throw new Error(`Invoice not found: ${documentId}`);
        }

        if (!invoice.invoice_number) {
            throw new Error('Invoice must have an invoice number before PDF generation. Please finalize the invoice first.');
        }

        console.log(`Generating PDF for invoice ${invoice.invoice_number}...`);

        // Erstelle die PDF
        const invoicePdf = new InvoicePdf(invoice);
        await invoicePdf.init();

        if (!invoicePdf.filepath) {
            throw new Error('PDF generation failed: No filepath returned');
        }

        // Prüfe, ob die PDF-Datei tatsächlich erstellt wurde
        const fs = require('fs');
        if (!fs.existsSync(invoicePdf.filepath)) {
            throw new Error(`PDF file not found at path: ${invoicePdf.filepath}`);
        }

        console.log(`PDF successfully created at: ${invoicePdf.filepath}`);

        return {
            invoice,
            pdfPath: invoicePdf.filepath,
            fileName: `Invoice_${invoice.invoice_number}.pdf`
        };
    }

    async finalize(documentId: string) {
        const updatedInvoice = await this.generateInvoiceNumber(documentId);



        // PDF Generierung implementieren
        try {
            console.log(`Generiere PDF für Rechnung ${updatedInvoice?.invoice_number}...`);
            console.log('updatedInvoice:', JSON.stringify(updatedInvoice, null, 2));

            const invoicePdf = new InvoicePdf(updatedInvoice);
            console.log('InvoicePdf Instanz erstellt');

            await invoicePdf.init();
            console.log('InvoicePdf.init() abgeschlossen');

            // Überprüfe, ob die PDF-Datei erfolgreich erstellt wurde
            console.log(`invoicePdf.filepath = ${invoicePdf.filepath}`);
            if (!invoicePdf.filepath) {
                console.error(`Fehler: PDF-Datei wurde nicht erstellt. Filepath ist undefined.`);
                return updatedInvoice;
            }

            console.log(`PDF erfolgreich erstellt unter: ${invoicePdf.filepath}`);

            // Überprüfe, ob die Datei existiert
            if (!fs.existsSync(invoicePdf.filepath)) {
                console.error(`Fehler: PDF-Datei existiert nicht unter dem angegebenen Pfad: ${invoicePdf.filepath}`);
                return updatedInvoice;
            }

            // Dateiname erstellen
            const fileName = `Rechnung_${updatedInvoice?.invoice_number}.pdf`;



            // Datei über den benutzerdefinierten Upload-Provider hochladen
            let uploadedFile;
            try {
                // Verwende den benutzerdefinierten Upload-Mechanismus von Strapi
                console.log(`Starte Upload der PDF-Datei: ${fileName}`);

                // Datei einlesen
                const fileBuffer = fs.readFileSync(invoicePdf.filepath);
                console.log(`PDF-Datei eingelesen, Größe: ${fileBuffer.length} Bytes`);

                // Verwende den Custom Upload Service
                const uploadResult = await strapi.service('api::invoice.custom-upload').uploadInvoicePdf(
                    updatedInvoice.documentId,
                    invoicePdf.filepath,
                    fileName
                );

                if (uploadResult.success) {
                    uploadedFile = uploadResult.file;
                    console.log('Upload erfolgreich:', uploadedFile.url);
                } else {
                    throw new Error('Failed to upload PDF file');
                }
            } catch (uploadError) {
                console.error(`Fehler beim Upload der Datei: ${uploadError.message}`, uploadError);
                // Werfen wir den Fehler nicht, damit die Rechnung trotzdem erstellt wird
            }

            console.log(`PDF erfolgreich an Invoice ${documentId} angehängt`);

            // Invoice mit der Datei-ID aktualisieren und Status setzen
            if (uploadedFile) {
                const fileId = Array.isArray(uploadedFile) ? uploadedFile[0].id : uploadedFile.id;

                // Aktualisiere die Invoice mit der Datei und setze den Status auf unveränderlich
                await strapi.documents('api::invoice.invoice').update({
                    documentId: documentId,
                    data: {
                        file: fileId,
                        invoice_status: 'INMUTABLE_WRITTEN' // Rechnung ist jetzt unveränderlich
                    }
                });

                console.log(`Invoice ${documentId} updated with file ${fileId} and status INMUTABLE_WRITTEN`);
            }

            // Lösche die temporäre PDF-Datei
            try {
                fs.unlinkSync(invoicePdf.filepath);
                console.log(`Temporary PDF file deleted: ${invoicePdf.filepath}`);
            } catch (deleteError) {
                console.warn(`Could not delete temporary file: ${deleteError.message}`);
            }

            return updatedInvoice;
        } catch (error) {
            console.error(`Fehler beim Anhängen der PDF-Datei: ${error.message}`);
            // Trotz Fehler beim Anhängen die aktualisierte Rechnung zurückgeben
            return updatedInvoice;
        }
    }

    async setStatusToPaid(documentId: string) {
        const updatedInvoice = await strapi.documents('api::invoice.invoice').update({
            documentId: documentId,
            data: {
                invoice_status: 'PAID',
                paid_date: new Date()
            }
        });
        return updatedInvoice;
    }


}
