/**
 * PDF Generator Service für Invoices
 * 
 * Dieser Service stellt Hilfsfunktionen zur Verfügung, um PDFs für Invoices zu generieren
 */

import { InvoiceManager } from './invoice-manager';
import { InvoicePdf } from './invoicePdf';
import fs from 'fs';
import path from 'path';

export class PdfGeneratorService {
    
    /**
     * Generiert eine PDF für eine Invoice und lädt sie in Strapi hoch
     * @param invoiceId Die Document-ID der Invoice
     * @param force Überschreibt eine existierende PDF
     * @returns Promise mit Informationen über die generierte PDF
     */
    static async generateAndUploadPdf(invoiceId: string, force: boolean = false) {
        try {
            // Hole die Invoice mit allen benötigten Relationen
            const invoice = await strapi.documents('api::invoice.invoice').findOne({
                documentId: invoiceId,
                populate: {
                    mandant: {
                        populate: ['logo']
                    },
                    invoice_positions: true,
                    ocpi_cdr: true,
                    ocpi_session: true,
                    payment_session: true,
                    file: true
                }
            });

            if (!invoice) {
                throw new Error(`Invoice not found: ${invoiceId}`);
            }

            if (!invoice.invoice_number) {
                throw new Error('Invoice must have an invoice number before PDF generation. Please finalize the invoice first.');
            }

            // Prüfe, ob bereits eine PDF-Datei existiert
            const existingPdfFile = invoice.file?.find(file => 
                file.name?.includes('Invoice_') && file.ext === '.pdf'
            );

            if (existingPdfFile && !force) {
                return {
                    success: false,
                    message: 'PDF already exists for this invoice',
                    existingFile: existingPdfFile,
                    hint: 'Use force=true to regenerate the PDF'
                };
            }

            console.log(`Generating PDF for invoice ${invoice.invoice_number}...`);

            // Erstelle die PDF
            const invoicePdf = new InvoicePdf(invoice);
            await invoicePdf.init();

            if (!invoicePdf.filepath) {
                throw new Error('PDF generation failed: No filepath returned');
            }

            // Prüfe, ob die PDF-Datei tatsächlich erstellt wurde
            if (!fs.existsSync(invoicePdf.filepath)) {
                throw new Error(`PDF file not found at path: ${invoicePdf.filepath}`);
            }

            console.log(`PDF successfully created at: ${invoicePdf.filepath}`);

            // Lade die PDF-Datei in Strapi hoch mit dem benutzerdefinierten Upload Provider
            const fileName = `Invoice_${invoice.invoice_number}.pdf`;
            const fileBuffer = fs.readFileSync(invoicePdf.filepath);

            // Lösche existierende PDF-Datei falls vorhanden und force=true
            if (existingPdfFile && force) {
                try {
                    await strapi.plugins.upload.services.upload.remove(existingPdfFile);
                    console.log(`Existing PDF file removed: ${existingPdfFile.url}`);
                } catch (removeError) {
                    console.warn(`Could not remove existing PDF file: ${removeError.message}`);
                }
            }

            // Erstelle ein File-Upload-Objekt für den benutzerdefinierten Provider
            const uploadedFiles = await strapi.plugins.upload.services.upload.upload({
                data: {
                    fileInfo: {
                        alternativeText: `Rechnung ${invoice.invoice_number}`,
                        caption: `PDF-Rechnung für ${invoice.invoice_number}`,
                        name: fileName,
                    },
                    refId: invoice.documentId,
                    ref: 'api::invoice.invoice',
                    field: 'file'
                },
                files: {
                    buffer: fileBuffer,
                    name: fileName,
                    type: 'application/pdf',
                    size: fileBuffer.length,
                    hash: `invoice_${invoice.invoice_number}_${Date.now()}`,
                    ext: '.pdf',
                    mime: 'application/pdf'
                }
            });

            console.log(`PDF uploaded to Strapi:`, uploadedFiles);

            // Aktualisiere die Invoice mit dem Status
            const updatedInvoice = await strapi.documents('api::invoice.invoice').update({
                documentId: invoiceId,
                data: {
                    invoice_status: 'INMUTABLE_WRITTEN' // Setze Status auf unveränderlich
                },
                populate: {
                    file: true,
                    mandant: true
                }
            });

            // Lösche die temporäre Datei
            try {
                fs.unlinkSync(invoicePdf.filepath);
                console.log(`Temporary PDF file deleted: ${invoicePdf.filepath}`);
            } catch (deleteError) {
                console.warn(`Could not delete temporary file: ${deleteError.message}`);
            }

            return {
                success: true,
                message: 'PDF generated successfully',
                invoice: {
                    documentId: updatedInvoice.documentId,
                    invoice_number: updatedInvoice.invoice_number,
                    invoice_status: updatedInvoice.invoice_status
                },
                file: uploadedFiles[0] || uploadedFiles,
                pdfPath: invoicePdf.filepath
            };

        } catch (error) {
            console.error('Error generating PDF:', error);
            throw new Error(`Failed to generate PDF: ${error.message}`);
        }
    }

    /**
     * Prüft, ob eine Invoice bereits eine PDF hat
     * @param invoiceId Die Document-ID der Invoice
     * @returns Promise<boolean>
     */
    static async hasPdf(invoiceId: string): Promise<boolean> {
        const invoice = await strapi.documents('api::invoice.invoice').findOne({
            documentId: invoiceId,
            populate: {
                file: true
            }
        });

        if (!invoice) {
            return false;
        }

        const pdfFile = invoice.file?.find(file => 
            file.name?.includes('Invoice_') && file.ext === '.pdf'
        );

        return !!pdfFile;
    }

    /**
     * Listet alle Invoices ohne PDF auf
     * @returns Promise mit Array von Invoices ohne PDF
     */
    static async getInvoicesWithoutPdf() {
        const invoices = await strapi.documents('api::invoice.invoice').findMany({
            filters: {
                invoice_number: {
                    $notNull: true
                }
            },
            populate: {
                file: true,
                mandant: true
            }
        });

        const invoicesWithoutPdf = [];

        for (const invoice of invoices) {
            const hasPdf = invoice.file?.some(file => 
                file.name?.includes('Invoice_') && file.ext === '.pdf'
            );

            if (!hasPdf) {
                invoicesWithoutPdf.push({
                    documentId: invoice.documentId,
                    invoice_number: invoice.invoice_number,
                    invoice_status: invoice.invoice_status,
                    mandant: invoice.mandant?.name,
                    sum_gross: invoice.sum_gross,
                    createdAt: invoice.createdAt
                });
            }
        }

        return invoicesWithoutPdf;
    }
}

export default PdfGeneratorService;
