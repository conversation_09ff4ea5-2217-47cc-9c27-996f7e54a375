/**
 * invoice controller
 */

import { factories } from '@strapi/strapi';
import {InvoiceManager} from "../services/invoice-manager";
import {capturePayment} from "../../payment-session/services/payment-capture";
import {InvoicePdf} from "../services/invoicePdf";
import fs from 'fs';
import path from 'path';

export default factories.createCoreController('api::invoice.invoice', ({ strapi }) => ({

    // Überschreibe CREATE, UPDATE, DELETE um sie zu deaktivieren
    async create(ctx) {
        return ctx.methodNotAllowed('Creating invoices via API is not allowed. Invoices are created automatically through the business process.');
    },

    async update(ctx) {
        return ctx.methodNotAllowed('Updating invoices via API is not allowed. Invoices are immutable once created.');
    },

    async delete(ctx) {
        return ctx.methodNotAllowed('Deleting invoices via API is not allowed. Invoices must be preserved for legal compliance.');
    },

    /**
     * OCPI CDR-Endpoint GET (CPO → eMSP)
     * Liefert alle CDRs oder einen spezifischen CDR nach ID
     */
    async testendpunkt(ctx) {
        try {
            // Finde den ersten CDR
            const cdr = await strapi.documents('api::ocpi-cdr.ocpi-cdr').findFirst({
                populate: {
                    mandant: true,
                    payment_session: {
                        history: true,
                        populate: {
                        terminal: true
                        }
                    }
                }
            });

            if (!cdr) {
                return ctx.badRequest('No CDR found in database');
            }

            console.log('Found CDR:', cdr.documentId);


            const im = new InvoiceManager();
            const newInvoice = await im.createPreviewInvoice(cdr.payment_session.documentId);

            // Hole die Payter-Connection für das Terminal
            const terminalId = cdr?.payment_session.terminal?.serialNumber;
            if (!terminalId) {
                throw new Error(`Kein Terminal für Payment-Session ${cdr?.payment_session.documentId} gefunden`);
            }
            ctx.state.terminal = cdr?.payment_session.terminal;
            // Hole den API-Client für die Payter-API
            const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

            if (!apiClient) {
                throw new Error('Konnte keinen API-Client für die Payter-API erstellen');
            }
            let amountToCapture = newInvoice.sum_gross * 100;

            if(newInvoice.sum_gross * 100 > cdr.payment_session.blockedAmount) {
                amountToCapture = cdr.payment_session.blockedAmount
            }
            // Führe die Capture-Operation durch
            const captureResult = await capturePayment(
                apiClient,
                terminalId,
                cdr?.payment_session.paymentIntent,
                amountToCapture
            );

            // Überprüfe das Ergebnis der Capture-Operation
            if (!captureResult.success) {
                console.error(`Fehler beim Belasten der Kreditkarte: ${captureResult.message}`);


                const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                    cdr?.payment_session.documentId,
                    'capture_failed',
                    amountToCapture,
                    captureResult
                );

                // Aktualisiere die Payment-Session mit dem Fehlerstatus
                const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
                    documentId: cdr?.payment_session.documentId,
                    data: {
                        paymentSessionState: 'error',
                        history:updatedHistory
                    }
                });
                return {
                    success: false,
                    paymentSession: updatedPaymentSession,
                    error: captureResult.error,
                    message: captureResult.message
                };
            }
            // update payment_session and set amount to capture auf den newInvoice amount gross
            if (newInvoice && cdr.payment_session) {
                // Berechne den zu belastenden Betrag in Cent (Bruttobetrag)

                const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                    cdr?.payment_session.documentId,
                    {
                        action: 'capture',
                        amount: amountToCapture,
                        timestamp: new Date().toISOString(),
                        result: captureResult
                    }
                );

                // Aktualisiere die Payment-Session mit dem erfolgreichen Status
                const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
                    documentId: cdr.payment_session.documentId,
                    data: { paymentSessionState: 'captured',
                        capturedAmount: amountToCapture,
                        closedAt: new Date(),
                        history: updatedHistory}

                });

                console.log(`Payment session ${cdr.payment_session.documentId} updated with amount to capture: ${amountToCapture / 100} €`);
            }

            // Finalisiere die Rechnung
            const finalizedInvoice = await im.finalize(newInvoice?.documentId);
            const paidInvoice = await im.setStatusToPaid(finalizedInvoice.documentId);
            return ctx.send({
                message: 'Invoice created and finalized',
                invoice: paidInvoice
            });

        } catch (error) {
            console.error('Error in testendpunkt:', error);
            return ctx.badRequest(`Error creating invoice: ${error.message}`);
        }
    },

    /**
     * Generiert manuell eine PDF für eine bestehende Invoice
     * POST /api/invoices/:id/generate-pdf
     */
    async generatePdf(ctx) {
        try {
            const { id } = ctx.params;
            const { force = false } = ctx.request.body || {};

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Verwende den PDF Generator Service
            const result = await strapi.service('api::invoice.pdf-generator').generateAndUploadPdf(id, force);

            if (!result.success) {
                return ctx.badRequest({
                    message: result.message,
                    existingFile: result.existingFile,
                    hint: result.hint
                });
            }

            return ctx.send({
                message: result.message,
                file: result.file,
                invoice: result.invoice
            });

        } catch (error) {
            console.error('Generate PDF error:', error);
            return ctx.internalServerError({
                message: 'Failed to generate PDF',
                error: error.message
            });
        }
    },

    /**
     * Lädt eine PDF-Datei für eine Invoice hoch
     * POST /api/invoices/:id/upload-pdf
     */
    async uploadPdf(ctx) {
        try {
            const { id } = ctx.params;
            const { force = false } = ctx.request.body || {};

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Prüfe, ob eine Datei hochgeladen wurde
            const files = ctx.request.files;
            if (!files || !files.file) {
                return ctx.badRequest('No PDF file provided');
            }

            const file = Array.isArray(files.file) ? files.file[0] : files.file;

            // Prüfe, ob es sich um eine PDF-Datei handelt
            if (file.type !== 'application/pdf') {
                return ctx.badRequest('Only PDF files are allowed');
            }

            // Verwende den Invoice File Manager Service
            const result = await strapi.service('api::invoice.invoice-file-manager').uploadInvoicePdf(
                id,
                file.path,
                force
            );

            if (!result.success) {
                return ctx.badRequest({
                    message: result.message,
                    existingFile: result.existingFile,
                    hint: result.hint
                });
            }

            return ctx.send({
                message: result.message,
                file: result.file,
                invoice: result.invoice
            });

        } catch (error) {
            console.error('Upload PDF error:', error);
            return ctx.internalServerError({
                message: 'Failed to upload PDF',
                error: error.message
            });
        }
    },

    /**
     * Entfernt eine PDF-Datei von einer Invoice
     * DELETE /api/invoices/:id/pdf
     */
    async removePdf(ctx) {
        try {
            const { id } = ctx.params;
            const { fileId } = ctx.request.query;

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Verwende den Invoice File Manager Service
            const result = await strapi.service('api::invoice.invoice-file-manager').removeInvoicePdf(id, fileId);

            if (!result.success) {
                return ctx.badRequest({
                    message: result.message
                });
            }

            return ctx.send({
                message: result.message,
                removedFile: result.removedFile
            });

        } catch (error) {
            console.error('Remove PDF error:', error);
            return ctx.internalServerError({
                message: 'Failed to remove PDF',
                error: error.message
            });
        }
    },

    /**
     * Holt die PDF-URL für eine Invoice
     * GET /api/invoices/:id/pdf-url
     */
    async getPdfUrl(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Verwende den Invoice File Manager Service
            const result = await strapi.service('api::invoice.invoice-file-manager').getInvoicePdfUrl(id);

            if (!result.success) {
                return ctx.notFound({
                    message: result.message
                });
            }

            return ctx.send({
                url: result.url,
                file: result.file
            });

        } catch (error) {
            console.error('Get PDF URL error:', error);
            return ctx.internalServerError({
                message: 'Failed to get PDF URL',
                error: error.message
            });
        }
    },

    /**
     * Lädt eine bestehende PDF-Datei für eine Invoice herunter
     * GET /api/invoices/:id/download-pdf
     */
    async downloadPdf(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Hole die Invoice
            const invoice = await strapi.documents('api::invoice.invoice').findOne({
                documentId: id,
                populate: {
                    mandant: true
                }
            });

            if (!invoice) {
                return ctx.notFound('Invoice not found');
            }

            if (!invoice.invoice_number) {
                return ctx.badRequest('Invoice has no invoice number');
            }

            // Konstruiere den PDF-Pfad basierend auf der bestehenden Logik
            const date = new Date();
            const year = date.getFullYear();
            const month = date.getMonth() + 1;

            const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');
            const fileName = `Invoice_${invoice.invoice_number}.pdf`;
            const pdfPath = path.join(invoiceFolder, String(year), String(month), fileName);

            // Prüfe, ob die PDF-Datei existiert
            if (!fs.existsSync(pdfPath)) {
                return ctx.notFound({
                    message: 'PDF file not found',
                    hint: 'Use POST /invoices/:id/generate-pdf to create the PDF first',
                    expectedPath: pdfPath
                });
            }

            // Setze die entsprechenden Headers für PDF-Download
            ctx.set('Content-Type', 'application/pdf');
            ctx.set('Content-Disposition', `attachment; filename="${fileName}"`);

            // Lese und sende die PDF-Datei
            const fileBuffer = fs.readFileSync(pdfPath);
            ctx.body = fileBuffer;

        } catch (error) {
            console.error('Error downloading PDF:', error);
            return ctx.internalServerError({
                message: 'Failed to download PDF',
                error: error.message
            });
        }
    },

    /**
     * Findet alle Invoices, für die keine PDF-Datei existiert
     * GET /api/invoices/missing-pdfs
     */
    async findMissingPdfs(ctx) {
        try {
            // Hole alle Invoices
            const allInvoices = await strapi.documents('api::invoice.invoice').findMany({
                populate: {
                    mandant: true,
                    payment_session: true
                },
                sort: { createdAt: 'desc' }
            });

            // Filtere finalisierte Invoices mit Rechnungsnummer
            const invoices = allInvoices.filter(invoice =>
                invoice.invoice_number &&
                invoice.invoice_number.trim() !== '' &&
                (invoice.invoice_status === 'INMUTABLE_WRITTEN' || invoice.invoice_status === 'PAID')
            );

            const missingPdfs = [];
            const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');

            for (const invoice of invoices) {
                if (!invoice.invoice_number) continue;

                // Konstruiere den erwarteten PDF-Pfad
                const date = new Date(invoice.createdAt);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;

                const fileName = `Invoice_${invoice.invoice_number}.pdf`;
                const pdfPath = path.join(invoiceFolder, String(year), String(month), fileName);

                // Prüfe, ob die PDF-Datei existiert
                if (!fs.existsSync(pdfPath)) {
                    missingPdfs.push({
                        documentId: invoice.documentId,
                        invoice_number: invoice.invoice_number,
                        invoice_status: invoice.invoice_status,
                        createdAt: invoice.createdAt,
                        mandant: invoice.mandant?.name,
                        expectedPdfPath: pdfPath,
                        sum_gross: invoice.sum_gross,
                        payment_session_id: invoice.payment_session?.documentId
                    });
                }
            }

            return ctx.send({
                message: `Found ${missingPdfs.length} invoices without PDF files`,
                count: missingPdfs.length,
                total_invoices: invoices.length,
                missing_pdfs: missingPdfs
            });

        } catch (error) {
            console.error('Error finding missing PDFs:', error);
            return ctx.internalServerError({
                message: 'Failed to find missing PDFs',
                error: error.message
            });
        }
    }
}));

