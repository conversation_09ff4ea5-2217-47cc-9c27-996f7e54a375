/**
 * Strapi Upload Provider Plugin
 * Registriert den benutzerdefinierten Upload Provider
 */

import localDirectoryProvider from '../../providers/upload/local-directory';

export default () => ({
  register({ strapi }) {
    // Registriere den benutzerdefinierten Upload Provider
    strapi.plugin('upload').provider('local-directory', localDirectoryProvider);
  },

  bootstrap({ strapi }) {
    // Plugin-Initialisierung
    console.log('Custom Upload Provider Plugin loaded');
  },
});
