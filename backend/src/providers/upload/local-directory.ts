// Custom Strapi Upload Provider for flexible file storage
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';

interface ProviderOptions {
  rootDir: string;
  baseUrl: string;
  sizeLimit?: number;
  allowedTypes?: string[];
  subDirectories?: {
    [key: string]: string;
  };
}

interface StrapiFile {
  name: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: any;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  folderPath?: string;
  path?: string;
  buffer?: Buffer;
  stream?: Readable;
}

interface UploadResult {
  url: string;
  path?: string;
}

export default {
  init(config: ProviderOptions) {
    const rootDir = path.resolve(config.rootDir);
    const baseUrl = config.baseUrl || '/uploads';
    const sizeLimit = config.sizeLimit || 100 * 1024 * 1024; // 100MB default
    const allowedTypes = config.allowedTypes || [];
    const subDirectories = config.subDirectories || {};

    // Stelle sicher, dass das Hauptverzeichnis existiert
    if (!fs.existsSync(rootDir)) {
      fs.mkdirSync(rootDir, { recursive: true });
    }

    // Erstelle Unterverzeichnisse falls konfiguriert
    Object.values(subDirectories).forEach(subDir => {
      const fullSubDirPath = path.join(rootDir, subDir);
      if (!fs.existsSync(fullSubDirPath)) {
        fs.mkdirSync(fullSubDirPath, { recursive: true });
      }
    });

    const generateFileName = (originalName: string, hash?: string): string => {
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      const ext = path.extname(originalName);
      const baseName = path.basename(originalName, ext);

      if (hash) {
        return `${hash}${ext}`;
      }

      return `${timestamp}_${randomString}_${baseName}${ext}`;
    };

    const getSubDirectory = (file: StrapiFile): string => {
      // Bestimme Unterverzeichnis basierend auf MIME-Type oder anderen Kriterien
      if (file.mime?.startsWith('image/')) {
        return subDirectories.images || 'images';
      } else if (file.mime === 'application/pdf') {
        return subDirectories.documents || 'documents';
      } else if (file.mime?.startsWith('video/')) {
        return subDirectories.videos || 'videos';
      }
      return subDirectories.others || 'files';
    };

    const validateFile = (file: StrapiFile): void => {
      // Größenvalidierung
      if (file.size > sizeLimit) {
        throw new Error(`File size ${file.size} exceeds limit of ${sizeLimit} bytes`);
      }

      // MIME-Type Validierung
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.mime)) {
        throw new Error(`File type ${file.mime} is not allowed`);
      }
    };

    return {
      async upload(file: StrapiFile): Promise<void> {
        try {
          validateFile(file);

          const subDir = getSubDirectory(file);
          const fileName = generateFileName(file.name, file.hash);
          const subDirPath = path.join(rootDir, subDir);
          const filePath = path.join(subDirPath, fileName);

          // Stelle sicher, dass das Unterverzeichnis existiert
          if (!fs.existsSync(subDirPath)) {
            fs.mkdirSync(subDirPath, { recursive: true });
          }

          // Schreibe die Datei
          if (file.buffer) {
            fs.writeFileSync(filePath, file.buffer);
          } else if (file.stream) {
            await new Promise<void>((resolve, reject) => {
              const writeStream = fs.createWriteStream(filePath);
              file.stream!.pipe(writeStream);
              writeStream.on('finish', resolve);
              writeStream.on('error', reject);
            });
          } else {
            throw new Error('No file buffer or stream provided');
          }

          // Aktualisiere die Datei-Eigenschaften
          file.url = `${baseUrl}/${subDir}/${fileName}`;
          file.provider_metadata = {
            path: filePath,
            subDirectory: subDir
          };

          console.log(`File uploaded successfully: ${file.url}`);
        } catch (error) {
          console.error('Upload error:', error);
          throw error;
        }
      },

      async uploadStream(file: StrapiFile): Promise<void> {
        return this.upload(file);
      },

      async delete(file: StrapiFile): Promise<void> {
        try {
          const filePath = file.provider_metadata?.path;

          if (filePath && fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`File deleted successfully: ${filePath}`);
          } else {
            // Fallback: Versuche Pfad aus URL zu rekonstruieren
            const urlPath = file.url.replace(baseUrl, '');
            const reconstructedPath = path.join(rootDir, urlPath);

            if (fs.existsSync(reconstructedPath)) {
              fs.unlinkSync(reconstructedPath);
              console.log(`File deleted successfully (fallback): ${reconstructedPath}`);
            } else {
              console.warn(`File not found for deletion: ${filePath || reconstructedPath}`);
            }
          }
        } catch (error) {
          console.error('Delete error:', error);
          throw error;
        }
      },

      // Zusätzliche Methode für Strapi v4 Kompatibilität
      async checkFileSize(file: StrapiFile): Promise<void> {
        validateFile(file);
      }
    };
  }
};