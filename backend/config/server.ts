import cronTask from "./cron.task";


export default ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  cors: {
    enabled: true,
    origin: ['http://localhost:1337', 'http://0.0.0.0:1337',"http://jkhq.myddns.me","http://jkhq.myddns.me:1337"] // Fügen Sie hier Ihre erlaubten Origins hinzu
  },

  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS', ['myKeyA', 'myKeyB']),
  },
  cron: {
    enable: true,
    task: cronTask
  },
  publicURL: env('PUBLIC_URL', 'https://jr.payter.eulektro.de'),
  frontendURL: env('FRONTEND_URL', 'https://localhost:3000'),

  // Benutzerdefinierte Konfiguration für Rechnungsordner
  invoiceFolder: env('INVOICE_FOLDER', '/home/<USER>/terminal.eulektro.de/invoices'),
});
