export default ({ env }) => ({
  upload: {
    config: {
      provider: require('../src/providers/upload/local-directory'),
      providerOptions: {
        // Hauptverzeichnis für Uploads - kann über Umgebungsvariable konfiguriert werden
        rootDir: env('UPLOAD_ROOT_DIR', './storage/uploads'),
        
        // Basis-URL für den Zugriff auf Dateien
        baseUrl: env('UPLOAD_BASE_URL', '/storage'),
        
        // Maximale Dateigröße (100MB)
        sizeLimit: env.int('UPLOAD_SIZE_LIMIT', 100 * 1024 * 1024),
        
        // Erlaubte MIME-Types (leer = alle erlaubt)
        allowedTypes: env.array('UPLOAD_ALLOWED_TYPES', []),
        
        // Unterverzeichnisse für verschiedene Dateitypen
        subDirectories: {
          images: env('UPLOAD_IMAGES_DIR', 'images'),
          documents: env('UPLOAD_DOCUMENTS_DIR', 'documents'),
          videos: env('UPLOAD_VIDEOS_DIR', 'videos'),
          others: env('UPLOAD_OTHERS_DIR', 'files'),
          invoices: env('UPLOAD_INVOICES_DIR', 'invoices'),
          logos: env('UPLOAD_LOGOS_DIR', 'logos')
        }
      },
      actionOptions: {
        upload: {},
        uploadStream: {},
        delete: {}
      }
    }
  },
  
  // Weitere Plugin-Konfigurationen können hier hinzugefügt werden
  // Beispiel für andere Plugins:
  /*
  email: {
    config: {
      provider: 'nodemailer',
      providerOptions: {
        // Email-Konfiguration
      }
    }
  }
  */
});
