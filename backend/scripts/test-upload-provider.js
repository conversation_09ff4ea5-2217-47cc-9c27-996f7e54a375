/**
 * Test Script für den benutzerdefinierten Upload Provider
 * 
 * Dieses Script testet die Funktionalität des Upload Providers
 * und zeigt, wie Invoice PDFs verwaltet werden können.
 */

const fs = require('fs');
const path = require('path');

// Simuliere eine Test-PDF-Datei
function createTestPdf() {
  const testPdfContent = Buffer.from(`%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Invoice PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`);

  const testPdfPath = path.join(__dirname, 'test-invoice.pdf');
  fs.writeFileSync(testPdfPath, testPdfContent);
  return testPdfPath;
}

// Test-Funktionen
async function testUploadProvider() {
  console.log('🧪 Testing Custom Upload Provider...\n');

  try {
    // 1. Test: Invoice PDF generieren
    console.log('1️⃣ Testing PDF Generation...');
    
    // Hier würde normalerweise eine echte Invoice-ID verwendet
    const testInvoiceId = 'test_invoice_123';
    
    // Simuliere PDF-Generierung
    const testPdfPath = createTestPdf();
    console.log(`✅ Test PDF created at: ${testPdfPath}`);

    // 2. Test: Upload Provider Konfiguration prüfen
    console.log('\n2️⃣ Testing Upload Provider Configuration...');
    
    const uploadConfig = {
      rootDir: './storage/uploads',
      baseUrl: '/storage',
      subDirectories: {
        documents: 'documents',
        invoices: 'invoices'
      }
    };
    
    console.log('✅ Upload configuration:', JSON.stringify(uploadConfig, null, 2));

    // 3. Test: Verzeichnisstruktur erstellen
    console.log('\n3️⃣ Testing Directory Structure...');
    
    const uploadRoot = path.resolve(uploadConfig.rootDir);
    const documentsDir = path.join(uploadRoot, uploadConfig.subDirectories.documents);
    const invoicesDir = path.join(uploadRoot, uploadConfig.subDirectories.invoices);

    // Erstelle Verzeichnisse falls sie nicht existieren
    [uploadRoot, documentsDir, invoicesDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      } else {
        console.log(`✅ Directory exists: ${dir}`);
      }
    });

    // 4. Test: Datei-Upload simulieren
    console.log('\n4️⃣ Testing File Upload Simulation...');
    
    const fileName = `Invoice_TEST_${Date.now()}.pdf`;
    const targetPath = path.join(documentsDir, fileName);
    
    // Kopiere Test-PDF in Upload-Verzeichnis
    fs.copyFileSync(testPdfPath, targetPath);
    console.log(`✅ File uploaded to: ${targetPath}`);
    
    const fileUrl = `${uploadConfig.baseUrl}/documents/${fileName}`;
    console.log(`✅ File URL: ${fileUrl}`);

    // 5. Test: Datei-Metadaten
    console.log('\n5️⃣ Testing File Metadata...');
    
    const stats = fs.statSync(targetPath);
    const metadata = {
      name: fileName,
      size: stats.size,
      mime: 'application/pdf',
      url: fileUrl,
      path: targetPath,
      created: stats.birthtime,
      modified: stats.mtime
    };
    
    console.log('✅ File metadata:', JSON.stringify(metadata, null, 2));

    // 6. Test: Cleanup
    console.log('\n6️⃣ Testing Cleanup...');
    
    // Lösche Test-Dateien
    [testPdfPath, targetPath].forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`✅ Deleted: ${filePath}`);
      }
    });

    console.log('\n🎉 All tests passed successfully!');
    
    return {
      success: true,
      message: 'Upload provider test completed successfully',
      metadata
    };

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// API Test-Funktionen (für Verwendung mit laufendem Strapi)
async function testInvoicePdfApi(baseUrl = 'http://localhost:1337', apiToken = '') {
  console.log('🌐 Testing Invoice PDF API...\n');

  if (!apiToken) {
    console.log('⚠️  No API token provided. Skipping API tests.');
    return;
  }

  try {
    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    // 1. Test: Fehlende PDFs finden
    console.log('1️⃣ Testing missing PDFs endpoint...');
    
    const missingPdfsResponse = await fetch(`${baseUrl}/api/invoices/missing-pdfs`, {
      method: 'GET',
      headers
    });

    if (missingPdfsResponse.ok) {
      const missingPdfs = await missingPdfsResponse.json();
      console.log(`✅ Found ${missingPdfs.count} invoices without PDFs`);
    } else {
      console.log(`❌ Failed to fetch missing PDFs: ${missingPdfsResponse.status}`);
    }

    // 2. Test: PDF-URL abrufen (für erste Invoice)
    console.log('\n2️⃣ Testing PDF URL endpoint...');
    
    // Hier würde eine echte Invoice-ID verwendet
    const testInvoiceId = 'your_test_invoice_id';
    
    const pdfUrlResponse = await fetch(`${baseUrl}/api/invoices/${testInvoiceId}/pdf-url`, {
      method: 'GET',
      headers
    });

    if (pdfUrlResponse.ok) {
      const pdfData = await pdfUrlResponse.json();
      console.log(`✅ PDF URL: ${pdfData.url}`);
    } else if (pdfUrlResponse.status === 404) {
      console.log('ℹ️  No PDF found for test invoice (expected for new setup)');
    } else {
      console.log(`❌ Failed to get PDF URL: ${pdfUrlResponse.status}`);
    }

    console.log('\n🎉 API tests completed!');

  } catch (error) {
    console.error('\n❌ API test failed:', error.message);
  }
}

// Hauptfunktion
async function main() {
  console.log('🚀 Starting Upload Provider Tests\n');
  console.log('=' .repeat(50));

  // Lokale Tests
  const localTestResult = await testUploadProvider();
  
  console.log('\n' + '=' .repeat(50));
  
  // API Tests (optional)
  const apiToken = process.env.STRAPI_API_TOKEN;
  const baseUrl = process.env.STRAPI_BASE_URL || 'http://localhost:1337';
  
  if (apiToken) {
    await testInvoicePdfApi(baseUrl, apiToken);
  } else {
    console.log('ℹ️  Set STRAPI_API_TOKEN environment variable to run API tests');
  }

  console.log('\n' + '=' .repeat(50));
  console.log('✨ Test suite completed!');
  
  return localTestResult;
}

// Script ausführen wenn direkt aufgerufen
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testUploadProvider,
  testInvoicePdfApi,
  createTestPdf
};
